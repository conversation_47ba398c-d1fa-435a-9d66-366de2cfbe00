# Pull Request

## Summary

Brief description of the changes in this PR.

## Type of Change

Please check the type of change your PR introduces:

- [ ] 🐛 Bug fix (non-breaking change that fixes an issue)
- [ ] ✨ New feature (non-breaking change that adds functionality)
- [ ] 💥 Breaking change (fix or feature that would cause existing functionality to change)
- [ ] 📚 Documentation update (changes to documentation only)
- [ ] 🔧 Refactoring (code changes that neither fix a bug nor add a feature)
- [ ] ⚡ Performance improvement
- [ ] 🧪 Test addition or improvement
- [ ] 🚀 CI/CD or build process changes

## Related Issues

Closes #(issue number)
Related to #(issue number)

## Changes Made

- Change 1
- Change 2
- Change 3

## Testing

### Automated Testing
- [ ] All existing tests pass
- [ ] New tests added for new functionality
- [ ] Test coverage maintained or improved

### Manual Testing
- [ ] Tested on macOS
- [ ] Tested on Linux
- [ ] Tested on Windows (if applicable)
- [ ] Tested with Claude Code integration
- [ ] Audio notifications work (macOS only)
- [ ] Process management functions correctly

### Test Cases

Describe any specific test cases you ran:

1. Test case 1
2. Test case 2

## Performance Impact

- [ ] No performance impact
- [ ] Performance improvement (describe below)
- [ ] Potential performance regression (describe below)

Performance details:

## Breaking Changes

- [ ] No breaking changes
- [ ] Breaking changes (describe below and update docs)

Breaking change details:

## Documentation

- [ ] Documentation is up to date
- [ ] README.md updated (if needed)
- [ ] CONTRIBUTING.md updated (if needed)
- [ ] Code comments added/updated

## Security Considerations

- [ ] No security implications
- [ ] Security implications addressed (describe below)

Security details:

## Dependencies

- [ ] No new dependencies
- [ ] New dependencies added (list below)
- [ ] Dependencies updated (list below)

Dependency changes:

## Checklist

### Code Quality
- [ ] Code follows the project's style guidelines
- [ ] Self-review of code completed
- [ ] Code is properly commented
- [ ] No TODO comments left in production code

### Functionality
- [ ] Changes are backwards compatible
- [ ] Error handling is appropriate
- [ ] Memory management is proper (no leaks)
- [ ] Process cleanup works correctly

### Integration
- [ ] MCP protocol compliance maintained
- [ ] Claude Code integration works
- [ ] Cross-platform compatibility preserved

## Additional Notes

Any additional information that reviewers should know:

---

## For Reviewers

### Review Focus Areas
Please pay special attention to:
- [ ] Security implications
- [ ] Performance impact
- [ ] Cross-platform compatibility
- [ ] MCP protocol compliance

### Review Checklist
- [ ] Code review completed
- [ ] Testing verified
- [ ] Documentation reviewed
- [ ] Security considerations addressed