name: Feature Request
description: Suggest an idea for Sidekick
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature for Sidekick!

  - type: textarea
    id: problem
    attributes:
      label: Problem Description
      description: Is your feature request related to a problem? Please describe.
      placeholder: A clear and concise description of what the problem is. Ex. I'm always frustrated when [...]
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like
      placeholder: A clear and concise description of what you want to happen.
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternatives Considered
      description: Describe any alternative solutions or features you've considered
      placeholder: A clear and concise description of any alternative solutions or features you've considered.

  - type: dropdown
    id: component
    attributes:
      label: Component
      description: Which component would this feature affect?
      options:
        - Process Management
        - Audio Notifications
        - MCP Integration
        - Installation/Setup
        - Documentation
        - CI/CD
        - Other

  - type: dropdown
    id: priority
    attributes:
      label: Priority
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would significantly improve my workflow
        - High - Blocking my usage of Sidekick
      default: 1

  - type: textarea
    id: use-case
    attributes:
      label: Use Case
      description: Describe your specific use case for this feature
      placeholder: |
        Example:
        - I'm building AI agents that need to...
        - When working with long-running processes, I need to...
        - For my deployment pipeline, I require...

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context, screenshots, or examples about the feature request here.

  - type: checkboxes
    id: terms
    attributes:
      label: Pre-submission Checklist
      description: By submitting this feature request, you confirm that
      options:
        - label: I have searched existing issues to avoid duplicates
          required: true
        - label: This feature aligns with Sidekick's goals (MCP server for AI agents)
          required: true
        - label: I have provided sufficient detail for implementation consideration
          required: true