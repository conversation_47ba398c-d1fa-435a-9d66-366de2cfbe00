name: Bug Report
description: File a bug report to help us improve Sidekick
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this bug report!

  - type: input
    id: version
    attributes:
      label: Sidekick Version
      description: What version of Sidekick are you running?
      placeholder: e.g., v0.1.0
    validations:
      required: true

  - type: dropdown
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      options:
        - macOS (Intel)
        - macOS (Apple Silicon)
        - Linux (x86_64)
        - Linux (ARM64)
        - Windows
        - Other (please specify in description)
    validations:
      required: true

  - type: input
    id: go-version
    attributes:
      label: Go Version
      description: What version of Go are you using? (if building from source)
      placeholder: e.g., go1.23.4

  - type: input
    id: claude-version
    attributes:
      label: Claude Code Version
      description: What version of Claude Code are you using?
      placeholder: e.g., 1.2.3

  - type: textarea
    id: what-happened
    attributes:
      label: What happened?
      description: A clear and concise description of what the bug is.
      placeholder: Tell us what you see!
    validations:
      required: true

  - type: textarea
    id: expected-behavior
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened instead?
    validations:
      required: true

  - type: textarea
    id: steps-to-reproduce
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Configure Sidekick with '...'
        2. Run command '...'
        3. See error
    validations:
      required: true

  - type: textarea
    id: logs
    attributes:
      label: Relevant Logs
      description: Please copy and paste any relevant log output. This will be automatically formatted into code.
      render: shell

  - type: textarea
    id: mcp-config
    attributes:
      label: MCP Configuration
      description: Your Claude Code MCP configuration (remove any sensitive information)
      render: json

  - type: textarea
    id: additional-context
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.

  - type: checkboxes
    id: terms
    attributes:
      label: Pre-submission Checklist
      description: By submitting this issue, you agree that
      options:
        - label: I have searched existing issues to avoid duplicates
          required: true
        - label: I have provided all requested information
          required: true
        - label: This is not a security vulnerability (use SECURITY.md for those)
          required: true