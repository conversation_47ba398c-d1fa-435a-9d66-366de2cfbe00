# Sidekick Improvement TODO List

## Critical Issues (Priority 1)

### Error Handling & Silent Failures ✅ COMPLETED
- [x] Fix silent error handling in notifications.go (lines 108, 114)
- [x] Fix unhandled pipe close error in processes.go (line 1534)
- [x] Fixed 6 instances of ignored Process.Kill() errors
- [x] Add error context using fmt.Errorf("context: %w", err) pattern
- [x] Review and fix all ignored errors throughout codebase

### Remaining Error Handling Tasks
- [x] Add structured logging for error conditions ✅ COMPLETED
  - Created logger.go with ERROR, WARN, INFO levels
  - Created logs_page.go for TUI visualization
  - Integrated logs page into TUI navigation (press '3' or Tab to access)
  - Replaced all logIfNotTUI calls with appropriate logger calls
  - <PERSON>gger automatically disables console output when TUI is active
- [ ] Standardize error handling approach (choose between error returns vs mcp.NewToolResultError)
- [ ] Handle json.Marshal errors (currently ignored in 8 places)
- [ ] Create custom error types for better error discrimination

### Security Vulnerabilities
- [ ] Strengthen command injection protection in filter commands
- [ ] Add comprehensive argument sanitization for exec.Command
- [ ] Implement process isolation/sandboxing
- [ ] Add resource limits for spawned processes
- [ ] Add input validation for buffer sizes, delays, and other parameters
- [ ] Implement session authentication for SSE server

### Platform Support
- [ ] Implement proper Windows process group management using Job Objects
- [ ] Fix Windows implementation in process_windows.go
- [ ] Create cross-platform notification system with fallbacks
- [ ] Add support for more Unix signals (SIGHUP, SIGINT)

## High-Priority Improvements (Priority 2)

### Code Organization
- [ ] Refactor into proper package structure (process, tui, session, notification, server)
- [ ] Replace global variables with dependency injection or central App struct
- [ ] Separate business logic from UI code
- [ ] Create service layer between UI and process management

### Testing Coverage
- [ ] Add unit tests for ProcessTracker
- [ ] Add unit tests for SessionManager
- [ ] Add unit tests for NotificationManager
- [ ] Add integration tests for SSE server
- [ ] Add TUI interaction tests
- [ ] Create mock infrastructure for external dependencies
- [ ] Achieve at least 80% test coverage

### Performance Issues
- [ ] Implement event-driven TUI updates instead of polling
- [ ] Optimize RingBuffer to use circular buffer (avoid allocations)
- [ ] Add secondary indexes for process registry (by session ID)
- [ ] Profile and optimize memory usage

## Medium-Priority Enhancements (Priority 3)

### Missing Features
- [ ] Add CPU/memory usage monitoring for processes
- [ ] Implement process log persistence
- [ ] Add search/filter functionality in TUI
- [ ] Add export functionality for process logs
- [ ] Create configuration file support
- [ ] Improve process group management
- [ ] Add process resource usage graphs in TUI

### User Experience
- [ ] Standardize keyboard shortcuts across all pages
- [ ] Add status/error message area in TUI
- [ ] Implement breadcrumb navigation
- [ ] Add help screen with shortcuts
- [ ] Improve process grouping visualization
- [ ] Add color themes support

### Code Quality
- [ ] Create base Page interface/struct for TUI pages
- [ ] Centralize ProcessStatusInfo with color mappings
- [ ] Create time formatting utilities
- [ ] Remove code duplication in TUI pages
- [ ] Replace hardcoded values with configuration

## Long-Term Improvements (Priority 4)

### Architecture
- [ ] Implement proper dependency injection
- [ ] Create plugin architecture for extensibility
- [ ] Add support for custom process handlers
- [ ] Implement event bus for decoupling components
- [ ] Consider moving to a microservices architecture

### Observability
- [ ] Implement structured logging with levels
- [ ] Add metrics and telemetry
- [ ] Create debug mode
- [ ] Add performance profiling endpoints
- [ ] Implement distributed tracing

### Documentation
- [ ] Add package-level documentation
- [ ] Complete GoDoc comments for all exported functions
- [ ] Create architecture documentation
- [ ] Write API documentation for MCP tools
- [ ] Add user guide for TUI
- [ ] Create developer documentation

### Resource Management
- [ ] Fix potential goroutine leaks
- [ ] Ensure proper file descriptor cleanup
- [ ] Implement context cancellation pattern consistently
- [ ] Add resource usage monitoring for sidekick itself
- [ ] Implement automatic cleanup policies

## Quick Wins (Can be done anytime)
- [ ] Add more descriptive error messages
- [ ] Fix typos and improve variable names
- [ ] Add version command
- [ ] Improve startup messages
- [ ] Add ASCII art logo