run:
  timeout: 5m
  go: "1.23"
  modules-download-mode: readonly

linters-settings:
  errcheck:
    check-type-assertions: true
    check-blank: true
  
  goconst:
    min-len: 3
    min-occurrences: 3
  
  gocritic:
    enabled-tags:
      - diagnostic
      - experimental
      - opinionated
      - performance
      - style
  
  gocyclo:
    min-complexity: 15
  
  godot:
    scope: declarations
    exclude:
      - "^fixme:"
      - "^todo:"
  
  goimports:
    local-prefixes: sidekick
  
  gomnd:
    settings:
      mnd:
        checks:
          - argument
          - case
          - condition
          - operation
          - return
  
  gosec:
    excludes:
      - G204 # Subprocess launched with variable
      - G104 # Audit errors not checked
  
  lll:
    line-length: 120
  
  misspell:
    locale: US
  
  nolintlint:
    allow-leading-space: true
    allow-unused: false
    require-explanation: false
    require-specific: false
  
  revive:
    rules:
      - name: exported
        arguments:
          - "checkPrivateReceivers"
          - "sayRepetitiveInsteadOfStutters"

linters:
  disable-all: true
  enable:
    - bodyclose
    - exhaustive
    - gofmt
    - goimports
    - gosimple
    - govet
    - ineffassign
    - staticcheck
    - typecheck
    - unconvert
    - unused

issues:
  exclude-rules:
    - path: _test\.go
      linters:
        - gomnd
        - gocritic
        - lll
  
  exclude-use-default: false
  exclude:
    - 'Error return value of .((os\.)?std(out|err)\..*|.*Close|.*Flush|os\.Remove(All)?|.*printf?|os\.(Un)?Setenv). is not checked'
  
  max-issues-per-linter: 0
  max-same-issues: 0

severity:
  default-severity: error
  case-sensitive: false