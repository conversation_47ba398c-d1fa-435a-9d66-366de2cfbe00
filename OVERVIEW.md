# AIDevTools

<div align="center">

[![CI](https://github.com/eliezedeck/AIDevTools/workflows/CI/badge.svg)](https://github.com/eliezedeck/AIDevTools/actions)
[![Release](https://img.shields.io/github/v/release/eliezedeck/AIDevTools)](https://github.com/eliezedeck/AIDevTools/releases)
[![License: MIT](https://img.shields.io/badge/License-MIT-yellow.svg)](https://opensource.org/licenses/MIT)

**A collection of tools designed to enhance AI-powered software development workflows**

</div>

## 🎯 Mission

AIDevTools aims to bridge the gap between AI agents and development environments, providing purpose-built tools that enable AI systems to interact with development workflows more effectively and reliably.

## 🛠️ Available Tools

### 🤖 Sidekick MCP Server

**Production-ready MCP server for AI agent process management and notifications**

- **Process Management**: Spawn, monitor, and control long-running processes
- **Audio Notifications**: Smart alerts with TTS on macOS 
- **Enterprise Features**: Thread-safe operations, automatic cleanup, graceful shutdown
- **Cross-platform**: Process management works everywhere, notifications on macOS

[📖 Full Documentation](README.md) | [🚀 Quick Start](README.md#-quick-start)

## 🚧 Coming Soon

- **CodeQL Assistant**: AI-powered code quality and security analysis
- **DevEnv Manager**: Automated development environment setup and management
- **Pipeline Orchestrator**: AI-aware CI/CD pipeline management
- **Deployment Guardian**: Intelligent deployment monitoring and rollback

## 🤝 Contributing

We welcome contributions to any tool in the AIDevTools ecosystem! Please see our [Contributing Guide](CONTRIBUTING.md) for details on how to get involved.

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

---

<div align="center">

**Built with ❤️ for the AI development community**

[⭐ Star](https://github.com/eliezedeck/AIDevTools) • [🐛 Report Bug](https://github.com/eliezedeck/AIDevTools/issues) • [💡 Request Feature](https://github.com/eliezedeck/AIDevTools/issues)

</div>