{"permissions": {"allow": ["<PERSON><PERSON>(mkdir:*)", "Bash(go build:*)", "Bash(go mod:*)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(mv:*)", "<PERSON><PERSON>(rmdir:*)", "mcp__sidekick__notifications_speak", "Bash(git add:*)", "<PERSON><PERSON>(gh run list:*)", "Bash(gh run view:*)", "Bash(gh run watch:*)", "Bash(go fmt:*)", "Bash(gh release view:*)", "mcp__sidekick__spawn_process", "mcp__sidekick__get_full_process_output", "mcp__sidekick__get_partial_process_output", "mcp__sidekick__kill_process", "<PERSON><PERSON>(go test:*)", "Bash(grep:*)", "Bash(ls:*)", "<PERSON><PERSON>(make test:*)", "<PERSON><PERSON>(make:*)", "WebFetch(domain:modelcontextprotocol.io)", "WebFetch(domain:github.com)", "<PERSON><PERSON>(./sidekick --help)", "Ba<PERSON>(go vet:*)", "<PERSON><PERSON>(curl:*)", "Bash(go get:*)", "<PERSON><PERSON>(timeout:*)", "Bash(rm:*)", "<PERSON><PERSON>(pkill:*)", "mcp__sidekick__list_processes", "mcp__sidekick__get_process_status", "mcp__sidekick__send_process_input", "mcp__sidekick__spawn_multiple_processes", "Bash(rg:*)", "<PERSON><PERSON>(sed:*)", "WebFetch(domain:pkg.go.dev)", "<PERSON><PERSON>(true)", "WebFetch(domain:raw.githubusercontent.com)"], "deny": []}}